import { ForbiddenError, subject } from '@casl/ability';
import { singleton } from 'tsyringe';

import { toDbIds } from '@malou-io/package-models';
import { CaslSubject } from '@malou-io/package-utils';

import { RequestWithPermissions } from ':modules/auth/auth.interfaces';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';

@singleton()
export class ArchiveReviewsUseCase {
    constructor(private readonly _reviewsRepository: ReviewsRepository) {}

    async execute(reviewIds: string[], req: RequestWithPermissions): Promise<{ archivedCount: number }> {
        // Get all reviews by IDs to validate they exist and check permissions
        const reviews = await this._reviewsRepository.getReviewsByIds(reviewIds);

        if (reviews.length !== reviewIds.length) {
            throw new Error('Some reviews were not found');
        }

        // Check CASL permissions for each review
        if (!req.userRestaurantsAbility) {
            throw new Error('User restaurants ability is not defined');
        }

        for (const review of reviews) {
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                'update',
                subject(CaslSubject.REVIEW, { restaurantId: review.restaurantId })
            );
        }

        // Perform bulk update to archive all reviews
        const result = await this._reviewsRepository.updateMany({
            filter: { _id: { $in: toDbIds(reviewIds) } },
            update: { archived: true },
        });

        return { archivedCount: result.modifiedCount };
    }
}
