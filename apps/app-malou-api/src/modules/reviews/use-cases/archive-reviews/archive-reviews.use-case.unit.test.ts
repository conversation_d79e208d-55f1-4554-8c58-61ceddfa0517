import 'reflect-metadata';

import { container } from 'tsyringe';

import { createAbility } from '@casl/ability';
import { newDbId } from '@malou-io/package-models';
import { CaslAction, CaslSubject, PlatformPresenceStatus } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { RequestWithPermissions } from ':modules/auth/auth.interfaces';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { ArchiveReviewsUseCase } from ':modules/reviews/use-cases/archive-reviews/archive-reviews.use-case';

describe('ArchiveReviewsUseCase', () => {
    beforeEach(() => {
        container.reset();
        registerRepositories(['RestaurantsRepository', 'ReviewsRepository']);
    });

    describe('execute', () => {
        it('should archive multiple reviews successfully', async () => {
            const archiveReviewsUseCase = container.resolve(ArchiveReviewsUseCase);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .platformPresenceStatus(PlatformPresenceStatus.FOUND)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .archived(false)
                                    .build(),
                                getDefaultReview()
                                    .platformPresenceStatus(PlatformPresenceStatus.FOUND)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .archived(false)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: undefined,
            });

            await testCase.build();
            const seeds = testCase.getSeededObjects();

            // Create mock request with permissions
            const mockReq = {
                userRestaurantsAbility: createAbility([
                    {
                        action: CaslAction.UPDATE,
                        subject: CaslSubject.REVIEW,
                        conditions: { restaurantId: seeds.restaurants[0]._id },
                    },
                ]),
            } as RequestWithPermissions;

            const reviewIds = seeds.reviews.map((review) => review._id.toString());
            const result = await archiveReviewsUseCase.execute(reviewIds, mockReq);

            expect(result.archivedCount).toBe(2);
        });

        it('should throw error when reviews are not found', async () => {
            const archiveReviewsUseCase = container.resolve(ArchiveReviewsUseCase);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    reviews: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult: undefined,
            });

            await testCase.build();

            const mockReq = {
                userRestaurantsAbility: createAbility([]),
            } as RequestWithPermissions;

            const nonExistentReviewIds = [newDbId().toString(), newDbId().toString()];

            await expect(archiveReviewsUseCase.execute(nonExistentReviewIds, mockReq)).rejects.toThrow(
                'Some reviews were not found'
            );
        });
    });
});
